# Sonner Toast Migration Guide

This guide explains how to migrate from the custom error/toast components to the new Sonner-based toast system.

## Overview

The application has been updated to use [Sonner](https://sonner.emilkowal.ski/) for all toast notifications, replacing the custom error toast components. Sonner provides better UX, performance, and accessibility.

## What Changed

### Removed Components

-   ❌ `ErrorToastContainer` - Removed completely
-   ❌ `SuccessToast` - Removed completely
-   ❌ Custom toast animations - Replaced with Sonner
-   ❌ Complex error management provider - Simplified

### New Utility Functions

-   ✅ `showErrorToast()` - Show error toasts with severity mapping
-   ✅ `showSuccessToast()` - Show success toasts
-   ✅ `showInfoToast()` - Show info toasts
-   ✅ `showWarningToast()` - Show warning toasts
-   ✅ `showLoadingToast()` - Show loading toasts
-   ✅ `showPromiseToast()` - Show promise-based toasts
-   ✅ `dismissToast()` / `dismissAllToasts()` - Dismiss toasts

## Migration Steps

### 1. Update Provider (Already Done)

The main layout has been updated to include Sonner Toaster:

```tsx
// OLD
<ErrorManagementProvider config={...}>
  {children}
  <Toaster /> {/* Radix UI toaster */}
</ErrorManagementProvider>

// NEW
<ErrorManagementProvider config={...}>
  {children}
</ErrorManagementProvider>
<Toaster
  position="top-right"
  richColors
  closeButton
  expand={false}
  visibleToasts={5}
  toastOptions={{...}}
/>
```

### 2. Update Component Usage

#### Simple Toast Usage

```tsx
// OLD
import { useToast } from '@/components/ui/use-toast';
const { toast } = useToast();
toast({ title: 'Success', description: 'Operation completed' });

// NEW
import { showSuccessToast } from '@/lib/toast';
showSuccessToast('Operation completed');
```

#### Error Toast Usage

```tsx
// OLD
import { useError } from '@/contexts/error-context';
const { addError } = useError();
addError(new Error('Something failed'));

// NEW - Auto-shows via error context integration
import { useError } from '@/contexts/error-context';
const { addError } = useError();
addError(new Error('Something failed')); // Automatically shows Sonner toast

// OR manual usage
import { showErrorToast } from '@/lib/toast';
showErrorToast('Something failed', {
	onRetry: () => console.log('Retrying...'),
});
```

#### Loading States

```tsx
// OLD
const { toast } = useToast();
const loadingToast = toast({ title: 'Loading...' });
// Later: dismiss manually

// NEW
import { useSonnerToast } from '@/hooks/use-sonner-error-toast';
const { showLoading, promise } = useSonnerToast();

// Simple loading
const toastId = showLoading('Processing...');

// Promise-based (recommended)
promise(fetchData(), {
	loading: 'Fetching data...',
	success: 'Data loaded successfully!',
	error: 'Failed to load data',
});
```

### 3. Error Severity Mapping

Sonner automatically maps error severities to appropriate toast types:

-   `ErrorSeverity.CRITICAL` → Error toast (no auto-dismiss)
-   `ErrorSeverity.HIGH` → Error toast (no auto-dismiss)
-   `ErrorSeverity.MEDIUM` → Warning toast (8s duration)
-   `ErrorSeverity.LOW` → Info toast (5s duration)

### 4. Styling

Custom styling is automatically applied via CSS classes in `globals.css`:

-   `.sonner-toast` - Base toast styling
-   `.sonner-toast-action` - Action button styling
-   `.sonner-toast-description` - Description text styling
-   `[data-type="error"]` - Error-specific styling
-   `[data-type="success"]` - Success-specific styling

## API Reference

### useSonnerToast Hook

```tsx
const {
	showError, // (error, options?) => void
	showSuccess, // (message, duration?) => void
	showInfo, // (message, description?, duration?) => void
	showWarning, // (message, description?, duration?) => void
	showLoading, // (message, description?) => toastId
	dismissToast, // (toastId?) => void
	dismissAllToasts, // () => void
	promise, // (promise, messages, options?) => void
} = useSonnerToast();
```

### useSonnerErrorToast Hook

```tsx
// Auto-integrates with error context
const toastFunctions = useSonnerErrorToast({
	maxToasts: 5, // Max concurrent toasts
	autoShowErrors: true, // Auto-show errors from context
	showRetryForSeverities: [ErrorSeverity.HIGH, ErrorSeverity.MEDIUM],
	defaultDuration: {
		[ErrorSeverity.CRITICAL]: 0, // No auto-dismiss
		[ErrorSeverity.HIGH]: 0, // No auto-dismiss
		[ErrorSeverity.MEDIUM]: 8000, // 8 seconds
		[ErrorSeverity.LOW]: 5000, // 5 seconds
	},
});
```

### Convenience Functions

```tsx
import {
	showErrorToast,
	showSuccessToast,
	showInfoToast,
	showWarningToast,
} from '@/lib/sonner-toast';

// Direct usage without hooks
showErrorToast('Error message', { onRetry: () => {} });
showSuccessToast('Success message', { duration: 3000 });
```

### SonnerToastService

```tsx
import { SonnerToastService } from '@/lib/sonner-toast';

// Advanced usage
SonnerToastService.showError(appError, options);
SonnerToastService.promise(promise, messages, options);
SonnerToastService.dismiss(toastId);
SonnerToastService.dismissAll();
```

## Configuration

### Provider Configuration

```tsx
<SonnerErrorManagementProvider
	config={{
		// Error management config
		enableErrorReporting: true,
		enableNetworkDetection: true,
		enableApiInterception: true,

		// Toast-specific config
		toastPosition: 'top-right',
		maxToasts: 5,
		autoShowErrors: true,
	}}
>
	{children}
</SonnerErrorManagementProvider>
```

### Toast Positioning

Available positions:

-   `'top-left'`
-   `'top-right'` (default)
-   `'bottom-left'`
-   `'bottom-right'`
-   `'top-center'`
-   `'bottom-center'`

## Benefits

1. **Better UX**: Smooth animations, better positioning, swipe-to-dismiss
2. **Accessibility**: Built-in ARIA support, keyboard navigation
3. **Performance**: Optimized rendering, automatic cleanup
4. **Consistency**: Unified toast system across the app
5. **Developer Experience**: Simpler API, better TypeScript support
6. **Customization**: Easy theming via CSS variables

## Backward Compatibility

The old toast components are still available but marked as deprecated:

```tsx
// Still works but deprecated
import { ErrorToastContainer } from '@/components/error';
```

## Examples

See `src/components/examples/sonner-toast-example.tsx` for comprehensive usage examples.

## Troubleshooting

### Toasts Not Showing

-   Ensure `SonnerErrorManagementProvider` is wrapping your app
-   Check that you're using the correct import paths
-   Verify error context integration is enabled

### Styling Issues

-   Check CSS custom properties are defined
-   Ensure dark mode classes are applied correctly
-   Verify Tailwind CSS is processing the toast classes

### Performance Issues

-   Use `dismissAllToasts()` to clear old toasts
-   Consider reducing `maxToasts` if showing many toasts
-   Use promise-based toasts for async operations

## Support

For questions or issues with the Sonner toast system, check:

1. This migration guide
2. The example component
3. Sonner documentation: https://sonner.emilkowal.ski/
4. Error context documentation in the codebase
