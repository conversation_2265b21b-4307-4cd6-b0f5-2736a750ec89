# Toast Context Guide

Hướng dẫn sử dụng Toast Context cho toàn bộ dự án Vocab.

## Tổng quan

Dự án sử dụng Toast Context dựa trên thư viện [Sonner](https://sonner.emilkowal.ski/) để hiển thị thông báo toast thống nhất cho toàn bộ ứng dụng.

## Cài đặt

Toast Context đã được tích hợp sẵn trong layout chính của ứng dụng:

```tsx
// src/app/layout.tsx
<ToastProvider>
  <AuthProvider>
    {children}
  </AuthProvider>
</ToastProvider>
<Toaster
  position="top-right"
  richColors
  closeButton
  expand={false}
  visibleToasts={5}
/>
```

## Cách sử dụng

### 1. Import hook

```tsx
import { useToast } from '@/contexts/toast-context';
```

### 2. Sử dụng trong component

```tsx
function MyComponent() {
  const { 
    showError, 
    showSuccess, 
    showInfo, 
    showWarning, 
    showLoading, 
    showPromise, 
    dismiss, 
    dismissAll 
  } = useToast();

  const handleSuccess = () => {
    showSuccess('Thao tác thành công!');
  };

  const handleError = () => {
    showError('Có lỗi xảy ra!', {
      onRetry: () => {
        // Logic retry
        console.log('Đang thử lại...');
      }
    });
  };

  return (
    <div>
      <button onClick={handleSuccess}>Success Toast</button>
      <button onClick={handleError}>Error Toast</button>
    </div>
  );
}
```

## API Reference

### showError(error, options?)

Hiển thị toast lỗi với mapping severity tự động:

```tsx
// String error
showError('Lỗi đơn giản');

// Error object
showError(new Error('JavaScript Error'));

// AppError với severity mapping
showError(appError, {
  onRetry: () => console.log('Retry'),
  showErrorId: true,
  duration: 5000
});
```

**Severity Mapping:**
- `CRITICAL/HIGH` → Error toast (không tự động đóng)
- `MEDIUM` → Warning toast (8s)
- `LOW` → Info toast (5s)

### showSuccess(message, options?)

```tsx
showSuccess('Thành công!', { duration: 3000 });
```

### showInfo(message, description?, duration?)

```tsx
showInfo('Thông tin', 'Mô tả chi tiết', 5000);
```

### showWarning(message, description?, duration?)

```tsx
showWarning('Cảnh báo', 'Vui lòng kiểm tra lại', 6000);
```

### showLoading(message, description?)

```tsx
const toastId = showLoading('Đang xử lý...', 'Vui lòng đợi');
```

### showPromise(promise, messages, options?)

```tsx
showPromise(
  fetchData(),
  {
    loading: 'Đang tải dữ liệu...',
    success: 'Tải thành công!',
    error: 'Tải thất bại!'
  },
  {
    loadingDescription: 'Có thể mất vài giây',
    successDescription: 'Dữ liệu đã được cập nhật',
    errorDescription: 'Vui lòng thử lại sau'
  }
);
```

### dismiss(toastId?) & dismissAll()

```tsx
// Đóng toast cụ thể
dismiss(toastId);

// Đóng tất cả toast
dismissAll();
```

## Ví dụ thực tế

### 1. Xử lý API call

```tsx
const handleApiCall = async () => {
  try {
    const result = await showPromise(
      apiCall(),
      {
        loading: 'Đang gửi yêu cầu...',
        success: 'Gửi thành công!',
        error: 'Gửi thất bại!'
      }
    );
    console.log(result);
  } catch (error) {
    // Promise toast đã xử lý error
  }
};
```

### 2. Form validation

```tsx
const handleSubmit = (data) => {
  if (!data.email) {
    showWarning('Email là bắt buộc', 'Vui lòng nhập email hợp lệ');
    return;
  }
  
  showSuccess('Form đã được gửi thành công!');
};
```

### 3. Network status

```tsx
useEffect(() => {
  const handleOnline = () => {
    showSuccess('Kết nối đã được khôi phục', { duration: 3000 });
  };
  
  const handleOffline = () => {
    showWarning(
      'Mất kết nối mạng',
      'Một số tính năng có thể không khả dụng',
      0 // Không tự động đóng
    );
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}, []);
```

## Styling

Toast được style tự động theo theme của ứng dụng thông qua CSS variables:

```css
/* src/app/globals.css */
.sonner-toast {
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  backdrop-filter: blur(8px);
}

[data-type='error'] .sonner-toast {
  border-left: 4px solid hsl(var(--destructive));
}

[data-type='success'] .sonner-toast {
  border-left: 4px solid hsl(var(--primary));
}
```

## Best Practices

1. **Sử dụng severity mapping**: Để AppError tự động map sang loại toast phù hợp
2. **Cung cấp retry cho errors**: Thêm `onRetry` cho các lỗi có thể retry
3. **Sử dụng showPromise**: Cho các async operations để UX tốt hơn
4. **Giới hạn duration**: Đặt duration = 0 cho các thông báo quan trọng
5. **Dismiss khi cần**: Sử dụng dismissAll() khi chuyển trang hoặc logout

## Migration từ hệ thống cũ

```tsx
// CŨ
import { useToast } from '@/components/ui/use-toast';
const { toast } = useToast();
toast({ title: "Success", description: "Done" });

// MỚI
import { useToast } from '@/contexts/toast-context';
const { showSuccess } = useToast();
showSuccess('Done');
```

## Troubleshooting

1. **Toast không hiển thị**: Kiểm tra ToastProvider đã wrap component chưa
2. **Styling không đúng**: Kiểm tra CSS variables và theme
3. **TypeScript errors**: Đảm bảo import đúng từ `@/contexts/toast-context`

## Tích hợp với Error Context

Toast Context tự động tích hợp với Error Context để hiển thị lỗi:

```tsx
// Error sẽ tự động hiển thị toast thông qua Error Context
const { addError } = useError();
addError(new AppError('Lỗi API', 'API_ERROR', 500, ErrorSeverity.HIGH));
```
