'use client';

import { useError } from '@/contexts/error-context';
import { AppError, ErrorSeverity } from '@/lib/error-handling';
import { SonnerToastService, ToastErrorOptions } from '@/lib/sonner-toast';
import { useCallback, useEffect, useRef } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorToastConfig {
	maxToasts?: number;
	autoShowErrors?: boolean;
	showRetryForSeverities?: ErrorSeverity[];
	defaultDuration?: {
		[key in ErrorSeverity]?: number;
	};
}

interface UseErrorToastReturn {
	showError: (error: AppError | Error | string, options?: ToastErrorOptions) => void;
	showSuccess: (message: string, duration?: number) => void;
	showInfo: (message: string, description?: string, duration?: number) => void;
	showWarning: (message: string, description?: string, duration?: number) => void;
	showLoading: (message: string, description?: string) => string | number;
	dismissToast: (toastId?: string | number) => void;
	dismissAllToasts: () => void;
}

// ============================================================================
// HOOK
// ============================================================================

export function useSonnerErrorToast(config: ErrorToastConfig = {}): UseErrorToastReturn {
	const {
		maxToasts = 5,
		autoShowErrors = true,
		showRetryForSeverities = [ErrorSeverity.HIGH, ErrorSeverity.MEDIUM],
		defaultDuration = {
			[ErrorSeverity.CRITICAL]: 0, // Don't auto-dismiss
			[ErrorSeverity.HIGH]: 0, // Don't auto-dismiss
			[ErrorSeverity.MEDIUM]: 8000,
			[ErrorSeverity.LOW]: 5000,
		},
	} = config;

	const { errorState, removeError } = useError();
	const processedErrorIds = useRef(new Set<string>());

	// Auto-show errors from error context
	useEffect(() => {
		if (!autoShowErrors) return;

		const newErrors = errorState.errors.filter(
			error => !processedErrorIds.current.has(error.id)
		);

		// Limit the number of toasts shown at once
		const errorsToShow = newErrors.slice(-maxToasts);

		errorsToShow.forEach(error => {
			processedErrorIds.current.add(error.id);

			const canRetry = showRetryForSeverities.includes(error.severity);
			const duration = defaultDuration[error.severity];

			SonnerToastService.showError(error, {
				duration,
				onRetry: canRetry ? () => {
					// Remove the error from context when retrying
					removeError(error.id);
					// You could also trigger a retry action here if needed
				} : undefined,
				showErrorId: process.env.NODE_ENV === 'development',
			});
		});

		// Clean up processed error IDs for errors that are no longer in the context
		const currentErrorIds = new Set(errorState.errors.map(e => e.id));
		processedErrorIds.current = new Set(
			Array.from(processedErrorIds.current).filter(id => currentErrorIds.has(id))
		);
	}, [
		errorState.errors,
		autoShowErrors,
		maxToasts,
		showRetryForSeverities,
		defaultDuration,
		removeError,
	]);

	// Manual error showing
	const showError = useCallback((
		error: AppError | Error | string,
		options: ToastErrorOptions = {}
	) => {
		if (typeof error === 'string') {
			return SonnerToastService.showError(
				new AppError(error, 'MANUAL_ERROR', 400, ErrorSeverity.MEDIUM),
				options
			);
		}

		if (error instanceof AppError) {
			return SonnerToastService.showError(error, options);
		}

		// Convert regular Error to AppError
		const appError = new AppError(
			error.message || 'An unexpected error occurred',
			'MANUAL_ERROR',
			500,
			ErrorSeverity.HIGH
		);

		return SonnerToastService.showError(appError, options);
	}, []);

	// Success toast
	const showSuccess = useCallback((message: string, duration = 4000) => {
		return SonnerToastService.showSuccess(message, { duration });
	}, []);

	// Info toast
	const showInfo = useCallback((
		message: string,
		description?: string,
		duration = 5000
	) => {
		return SonnerToastService.showInfo(message, description, duration);
	}, []);

	// Warning toast
	const showWarning = useCallback((
		message: string,
		description?: string,
		duration = 6000
	) => {
		return SonnerToastService.showWarning(message, description, duration);
	}, []);

	// Loading toast
	const showLoading = useCallback((message: string, description?: string) => {
		return SonnerToastService.showLoading(message, description);
	}, []);

	// Dismiss specific toast
	const dismissToast = useCallback((toastId?: string | number) => {
		return SonnerToastService.dismiss(toastId);
	}, []);

	// Dismiss all toasts
	const dismissAllToasts = useCallback(() => {
		return SonnerToastService.dismissAll();
	}, []);

	return {
		showError,
		showSuccess,
		showInfo,
		showWarning,
		showLoading,
		dismissToast,
		dismissAllToasts,
	};
}

// ============================================================================
// CONVENIENCE HOOK FOR SIMPLE USAGE
// ============================================================================

/**
 * Simple hook that just provides toast functions without error context integration
 */
export function useSonnerToast() {
	return {
		showError: (error: AppError | Error | string, options?: ToastErrorOptions) => {
			if (typeof error === 'string') {
				return SonnerToastService.showError(
					new AppError(error, 'MANUAL_ERROR', 400, ErrorSeverity.MEDIUM),
					options
				);
			}
			if (error instanceof AppError) {
				return SonnerToastService.showError(error, options);
			}
			const appError = new AppError(
				error.message || 'An unexpected error occurred',
				'MANUAL_ERROR',
				500,
				ErrorSeverity.HIGH
			);
			return SonnerToastService.showError(appError, options);
		},
		showSuccess: (message: string, duration = 4000) => 
			SonnerToastService.showSuccess(message, { duration }),
		showInfo: (message: string, description?: string, duration = 5000) => 
			SonnerToastService.showInfo(message, description, duration),
		showWarning: (message: string, description?: string, duration = 6000) => 
			SonnerToastService.showWarning(message, description, duration),
		showLoading: (message: string, description?: string) => 
			SonnerToastService.showLoading(message, description),
		dismissToast: (toastId?: string | number) => SonnerToastService.dismiss(toastId),
		dismissAllToasts: () => SonnerToastService.dismissAll(),
		promise: SonnerToastService.promise,
	};
}

export default useSonnerErrorToast;
