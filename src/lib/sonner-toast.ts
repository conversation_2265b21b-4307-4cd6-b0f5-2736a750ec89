'use client';

import { AppError, ErrorSeverity } from '@/lib/error-handling';
import { toast } from 'sonner';
import { RefreshCw } from 'lucide-react';
import { createElement } from 'react';

// ============================================================================
// TYPES
// ============================================================================

export interface ToastErrorOptions {
	onRetry?: () => void | Promise<void>;
	showErrorId?: boolean;
	duration?: number;
	position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
}

export interface ToastSuccessOptions {
	duration?: number;
	position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
}

// ============================================================================
// SONNER TOAST SERVICE
// ============================================================================

export class SonnerToastService {
	/**
	 * Show error toast based on AppError severity
	 */
	static showError(error: AppError, options: ToastErrorOptions = {}) {
		const {
			onRetry,
			showErrorId = process.env.NODE_ENV === 'development',
			duration,
		} = options;

		const message = error.message;
		const description = showErrorId ? `Error ID: ${error.id.slice(-8)}` : undefined;

		// Create retry action if provided
		const action = onRetry ? {
			label: 'Retry',
			onClick: async () => {
				try {
					await onRetry();
				} catch (retryError) {
					console.error('Retry failed:', retryError);
				}
			},
		} : undefined;

		// Map error severity to toast type
		switch (error.severity) {
			case ErrorSeverity.CRITICAL:
			case ErrorSeverity.HIGH:
				return toast.error(message, {
					description,
					action,
					duration: duration || 0, // Don't auto-dismiss critical/high errors
				});

			case ErrorSeverity.MEDIUM:
				return toast.warning(message, {
					description,
					action,
					duration: duration || 8000,
				});

			case ErrorSeverity.LOW:
				return toast.info(message, {
					description,
					action,
					duration: duration || 5000,
				});

			default:
				return toast.error(message, {
					description,
					action,
					duration: duration || 6000,
				});
		}
	}

	/**
	 * Show success toast
	 */
	static showSuccess(message: string, options: ToastSuccessOptions = {}) {
		const { duration = 4000 } = options;

		return toast.success(message, {
			duration,
		});
	}

	/**
	 * Show info toast
	 */
	static showInfo(message: string, description?: string, duration = 5000) {
		return toast.info(message, {
			description,
			duration,
		});
	}

	/**
	 * Show warning toast
	 */
	static showWarning(message: string, description?: string, duration = 6000) {
		return toast.warning(message, {
			description,
			duration,
		});
	}

	/**
	 * Show loading toast
	 */
	static showLoading(message: string, description?: string) {
		return toast.loading(message, {
			description,
		});
	}

	/**
	 * Dismiss a specific toast
	 */
	static dismiss(toastId?: string | number) {
		return toast.dismiss(toastId);
	}

	/**
	 * Dismiss all toasts
	 */
	static dismissAll() {
		return toast.dismiss();
	}

	/**
	 * Show promise toast (for async operations)
	 */
	static promise<T>(
		promise: Promise<T>,
		messages: {
			loading: string;
			success: string | ((data: T) => string);
			error: string | ((error: any) => string);
		},
		options?: {
			loadingDescription?: string;
			successDescription?: string | ((data: T) => string);
			errorDescription?: string | ((error: any) => string);
		}
	) {
		return toast.promise(promise, {
			loading: messages.loading,
			success: messages.success,
			error: messages.error,
			...options,
		});
	}
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Show error toast - convenience function
 */
export const showErrorToast = (error: AppError | Error | string, options: ToastErrorOptions = {}) => {
	if (typeof error === 'string') {
		return toast.error(error, {
			duration: options.duration || 6000,
		});
	}

	if (error instanceof AppError) {
		return SonnerToastService.showError(error, options);
	}

	// Regular Error object
	return toast.error(error.message || 'An unexpected error occurred', {
		description: options.showErrorId && process.env.NODE_ENV === 'development' 
			? `Error: ${error.name}` 
			: undefined,
		duration: options.duration || 6000,
		action: options.onRetry ? {
			label: 'Retry',
			onClick: options.onRetry,
		} : undefined,
	});
};

/**
 * Show success toast - convenience function
 */
export const showSuccessToast = (message: string, options: ToastSuccessOptions = {}) => {
	return SonnerToastService.showSuccess(message, options);
};

/**
 * Show info toast - convenience function
 */
export const showInfoToast = (message: string, description?: string, duration = 5000) => {
	return SonnerToastService.showInfo(message, description, duration);
};

/**
 * Show warning toast - convenience function
 */
export const showWarningToast = (message: string, description?: string, duration = 6000) => {
	return SonnerToastService.showWarning(message, description, duration);
};

/**
 * Show loading toast - convenience function
 */
export const showLoadingToast = (message: string, description?: string) => {
	return SonnerToastService.showLoading(message, description);
};

// ============================================================================
// EXPORTS
// ============================================================================

export { SonnerToastService as toastService };
export default SonnerToastService;
