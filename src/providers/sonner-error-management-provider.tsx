'use client';

import { ErrorBoundary } from '@/components/error/error-boundary';
import { ErrorProvider } from '@/contexts/error-context';
import { useSonnerErrorToast } from '@/hooks/use-sonner-error-toast';
import { apiClient } from '@/lib/api-interceptor';
import { errorLogger } from '@/lib/error-handling';
import { initializeErrorReporting } from '@/lib/error-reporting';
import { networkDetector } from '@/lib/network-detector';
import { ReactNode, useEffect } from 'react';
import { Toaster } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

interface SonnerErrorManagementProviderProps {
	children: ReactNode;
	config?: {
		enableErrorReporting?: boolean;
		enableNetworkDetection?: boolean;
		enableApiInterception?: boolean;
		errorReportingEndpoint?: string;
		errorReportingApiKey?: string;
		environment?: string;
		buildVersion?: string;
		userId?: string;
		sessionId?: string;
		toastPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
		maxToasts?: number;
		autoShowErrors?: boolean;
	};
}

// ============================================================================
// SONNER TOAST INTEGRATION COMPONENT
// ============================================================================

function SonnerToastIntegration({ 
	maxToasts = 5, 
	autoShowErrors = true 
}: { 
	maxToasts?: number; 
	autoShowErrors?: boolean; 
}) {
	// Initialize the Sonner error toast hook to auto-show errors
	useSonnerErrorToast({
		maxToasts,
		autoShowErrors,
	});

	return null; // This component only handles side effects
}

// ============================================================================
// MAIN PROVIDER
// ============================================================================

export function SonnerErrorManagementProvider({ 
	children, 
	config = {} 
}: SonnerErrorManagementProviderProps) {
	const {
		enableErrorReporting = true,
		enableNetworkDetection = true,
		enableApiInterception = true,
		errorReportingEndpoint,
		errorReportingApiKey,
		environment = process.env.NODE_ENV || 'development',
		buildVersion = process.env.NEXT_PUBLIC_BUILD_VERSION,
		userId,
		sessionId,
		toastPosition = 'top-right',
		maxToasts = 5,
		autoShowErrors = true,
	} = config;

	// Initialize error management systems
	useEffect(() => {
		// Configure error logger
		errorLogger.configure({
			enableConsoleLogging: true,
			enableRemoteLogging: !!errorReportingEndpoint,
			remoteEndpoint: errorReportingEndpoint,
		});

		// Initialize error reporting
		if (enableErrorReporting && errorReportingEndpoint) {
			initializeErrorReporting({
				endpoint: errorReportingEndpoint,
				apiKey: errorReportingApiKey,
				environment,
				buildVersion,
				userId,
				sessionId,
			});

			errorLogger.info(
				'Error reporting initialized',
				{
					endpoint: errorReportingEndpoint,
					environment,
					buildVersion,
				},
				'SonnerErrorManagementProvider'
			);
		}

		// Initialize network detection
		if (enableNetworkDetection) {
			networkDetector.initialize({
				enableLogging: true,
				onStatusChange: (isOnline, networkInfo) => {
					if (isOnline) {
						errorLogger.info(
							'Network connection restored',
							{ networkInfo },
							'NetworkDetector'
						);
					} else {
						errorLogger.warn(
							'Network connection lost',
							{ networkInfo },
							'NetworkDetector'
						);
					}
				},
			});

			errorLogger.info(
				'Network detection initialized',
				{ isOnline: networkDetector.isOnline() },
				'SonnerErrorManagementProvider'
			);
		}

		// Initialize API interception
		if (enableApiInterception) {
			apiClient.initialize({
				enableLogging: true,
				enableRetry: true,
				retryAttempts: 3,
				retryDelay: 1000,
				onError: (error, config) => {
					errorLogger.error(
						'API request failed',
						error,
						{
							url: config?.url,
							method: config?.method,
							status: error.status,
						},
						'ApiClient'
					);
				},
				onRetry: (error, attempt, config) => {
					errorLogger.warn(
						`API request retry attempt ${attempt}`,
						error,
						{
							url: config?.url,
							method: config?.method,
							attempt,
						},
						'ApiClient'
					);
				},
			});

			errorLogger.info(
				'API interception initialized',
				{
					enableRetry: true,
					retryAttempts: 3,
				},
				'SonnerErrorManagementProvider'
			);
		}

		// Cleanup function
		return () => {
			if (enableNetworkDetection) {
				networkDetector.cleanup();
			}
		};
	}, [
		enableErrorReporting,
		enableNetworkDetection,
		enableApiInterception,
		errorReportingEndpoint,
		errorReportingApiKey,
		environment,
		buildVersion,
		userId,
		sessionId,
	]);

	return (
		<ErrorProvider>
			<ErrorBoundary
				level="page"
				name="RootErrorBoundary"
				onError={(error, errorInfo) => {
					errorLogger.error(
						'Root error boundary caught error',
						error,
						{ errorInfo },
						'RootErrorBoundary'
					);
				}}
			>
				{children}
				
				{/* Sonner Toaster Component */}
				<Toaster
					position={toastPosition}
					richColors
					closeButton
					expand={false}
					visibleToasts={maxToasts}
					toastOptions={{
						style: {
							background: 'hsl(var(--background))',
							color: 'hsl(var(--foreground))',
							border: '1px solid hsl(var(--border))',
						},
						className: 'sonner-toast',
						descriptionClassName: 'sonner-toast-description',
						actionButtonClassName: 'sonner-toast-action',
						cancelButtonClassName: 'sonner-toast-cancel',
					}}
				/>
				
				{/* Auto-show errors integration */}
				<SonnerToastIntegration 
					maxToasts={maxToasts} 
					autoShowErrors={autoShowErrors} 
				/>
			</ErrorBoundary>
		</ErrorProvider>
	);
}

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

export default SonnerErrorManagementProvider;
