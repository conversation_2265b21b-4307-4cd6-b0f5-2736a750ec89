'use client';

import { AppError, Error<PERSON><PERSON><PERSON>y, getUserFriendlyMessage } from '@/lib/error-handling';
import { motion } from 'framer-motion';
import { AlertCircle, Home, RefreshCw, ArrowLeft, Bug, WifiOff } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorPageProps {
	error?: AppError | Error | null;
	title?: string;
	description?: string;
	showRetry?: boolean;
	showGoHome?: boolean;
	showGoBack?: boolean;
	showReportBug?: boolean;
	onRetry?: () => void;
	onReportBug?: (error: AppError | Error) => void;
	className?: string;
}

interface OfflinePageProps {
	onRetry?: () => void;
}

interface NotFoundPageProps {
	resource?: string;
	onGoHome?: () => void;
}

// ============================================================================
// MAIN ERROR PAGE COMPONENT
// ============================================================================

export function ErrorPage({
	error,
	title,
	description,
	showRetry = true,
	showGoHome = true,
	showGoBack = true,
	showReportBug = false,
	onRetry,
	onReportBug,
	className = '',
}: ErrorPageProps) {
	const router = useRouter();
	const [isRetrying, setIsRetrying] = useState(false);

	// Determine error details
	const errorTitle = title || getErrorTitle(error);
	const errorDescription = description || getErrorDescription(error);
	const errorIcon = getErrorIcon(error);
	const canRetry = showRetry && (onRetry || error);

	const handleRetry = async () => {
		if (isRetrying) return;

		setIsRetrying(true);
		try {
			if (onRetry) {
				await Promise.resolve(onRetry());
			} else {
				// Default retry: reload the page
				window.location.reload();
			}
		} finally {
			setIsRetrying(false);
		}
	};

	const handleGoBack = () => {
		if (window.history.length > 1) {
			router.back();
		} else {
			router.push('/');
		}
	};

	const handleReportBug = () => {
		if (onReportBug && error) {
			onReportBug(error);
		}
	};

	return (
		<div
			className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4 ${className}`}
		>
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="max-w-2xl w-full"
			>
				<div className="bg-white dark:bg-gray-800 shadow-xl rounded-lg p-8 text-center">
					{/* Error Icon */}
					<motion.div
						initial={{ scale: 0 }}
						animate={{ scale: 1 }}
						transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
						className="flex items-center justify-center w-16 h-16 mx-auto mb-6 bg-red-100 dark:bg-red-900/20 rounded-full"
					>
						{errorIcon}
					</motion.div>

					{/* Error Title */}
					<h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
						{errorTitle}
					</h1>

					{/* Error Description */}
					<p className="text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
						{errorDescription}
					</p>

					{/* Error ID (for debugging) */}
					{error instanceof AppError && error.id && (
						<p className="text-xs text-gray-400 dark:text-gray-500 mb-6 font-mono">
							Error ID: {error.id}
						</p>
					)}

					{/* Action Buttons */}
					<div className="space-y-3">
						{canRetry && (
							<motion.button
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
								onClick={handleRetry}
								disabled={isRetrying}
								className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
							>
								<RefreshCw
									className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`}
								/>
								{isRetrying ? 'Retrying...' : 'Try Again'}
							</motion.button>
						)}

						<div className="flex space-x-3">
							{showGoBack && (
								<motion.button
									whileHover={{ scale: 1.02 }}
									whileTap={{ scale: 0.98 }}
									onClick={handleGoBack}
									className="flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
								>
									<ArrowLeft className="h-4 w-4 mr-2" />
									Go Back
								</motion.button>
							)}

							{showGoHome && (
								<Link href="/" className="flex-1">
									<motion.button
										whileHover={{ scale: 1.02 }}
										whileTap={{ scale: 0.98 }}
										className="w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
									>
										<Home className="h-4 w-4 mr-2" />
										Home
									</motion.button>
								</Link>
							)}
						</div>

						{showReportBug && error && (
							<motion.button
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
								onClick={handleReportBug}
								className="w-full bg-orange-100 hover:bg-orange-200 dark:bg-orange-900/20 dark:hover:bg-orange-900/30 text-orange-800 dark:text-orange-200 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
							>
								<Bug className="h-4 w-4 mr-2" />
								Report Bug
							</motion.button>
						)}
					</div>

					{/* Development Details */}
					{process.env.NODE_ENV === 'development' && error instanceof AppError && (
						<details className="mt-6 text-left">
							<summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
								Development Details
							</summary>
							<pre className="mt-2 text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-auto max-h-40">
								{JSON.stringify(error.toLogObject(), null, 2)}
							</pre>
						</details>
					)}
				</div>
			</motion.div>
		</div>
	);
}

// ============================================================================
// SPECIALIZED ERROR PAGES
// ============================================================================

export function OfflinePage({ onRetry }: OfflinePageProps) {
	return (
		<ErrorPage
			title="You're Offline"
			description="It looks like you've lost your internet connection. Please check your connection and try again."
			onRetry={onRetry}
			showGoHome={false}
			showGoBack={false}
		/>
	);
}

export function NotFoundPage({ resource = 'page', onGoHome }: NotFoundPageProps) {
	return (
		<ErrorPage
			title="Page Not Found"
			description={`The ${resource} you're looking for doesn't exist or has been moved.`}
			showRetry={false}
			showGoHome={true}
			showGoBack={true}
			onRetry={onGoHome}
		/>
	);
}

export function ServerErrorPage({ onRetry }: { onRetry?: () => void }) {
	return (
		<ErrorPage
			title="Server Error"
			description="Something went wrong on our end. Our team has been notified and is working to fix the issue."
			onRetry={onRetry}
			showReportBug={true}
		/>
	);
}

export function MaintenancePage() {
	return (
		<ErrorPage
			title="Under Maintenance"
			description="We're currently performing scheduled maintenance. Please check back in a few minutes."
			showRetry={true}
			showGoHome={false}
			showGoBack={false}
		/>
	);
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function getErrorTitle(error?: AppError | Error | null): string {
	if (!error) return 'Something went wrong';

	if (error instanceof AppError) {
		switch (error.category) {
			case ErrorCategory.NETWORK:
				return 'Connection Problem';
			case ErrorCategory.AUTHENTICATION:
				return 'Authentication Required';
			case ErrorCategory.AUTHORIZATION:
				return 'Access Denied';
			case ErrorCategory.NOT_FOUND:
				return 'Not Found';
			case ErrorCategory.VALIDATION:
				return 'Invalid Input';
			case ErrorCategory.SERVER:
				return 'Server Error';
			default:
				return 'Unexpected Error';
		}
	}

	return 'Something went wrong';
}

function getErrorDescription(error?: AppError | Error | null): string {
	if (!error) return 'An unexpected error occurred. Please try again.';

	if (error instanceof AppError) {
		return getUserFriendlyMessage(error);
	}

	return error.message || 'An unexpected error occurred. Please try again.';
}

function getErrorIcon(error?: AppError | Error | null) {
	if (!error) return <AlertCircle className="h-8 w-8 text-red-500" />;

	if (error instanceof AppError) {
		switch (error.category) {
			case ErrorCategory.NETWORK:
				return <WifiOff className="h-8 w-8 text-red-500" />;
			case ErrorCategory.AUTHENTICATION:
			case ErrorCategory.AUTHORIZATION:
				return <AlertCircle className="h-8 w-8 text-yellow-500" />;
			default:
				return <AlertCircle className="h-8 w-8 text-red-500" />;
		}
	}

	return <AlertCircle className="h-8 w-8 text-red-500" />;
}
