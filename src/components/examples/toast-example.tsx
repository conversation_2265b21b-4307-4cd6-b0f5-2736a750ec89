'use client';

import { AppError, ErrorSeverity } from '@/lib/error-handling';
import { useToast } from '@/contexts/toast-context';
import { Button } from '@/components/ui/button';

// ============================================================================
// TOAST EXAMPLE COMPONENT
// ============================================================================

export function ToastExample() {
	const { showError, showSuccess, showInfo, showWarning, showLoading, showPromise, dismissAll } =
		useToast();

	// Example error objects
	const criticalError = new AppError(
		'Critical system failure detected',
		'SYSTEM_CRITICAL',
		500,
		ErrorSeverity.CRITICAL
	);

	const highError = new AppError(
		'Failed to fetch user data',
		'API_ERROR',
		404,
		ErrorSeverity.HIGH
	);

	const mediumError = new AppError(
		'Form validation failed',
		'VALIDATION_ERROR',
		400,
		ErrorSeverity.MEDIUM
	);

	const lowError = new AppError(
		'Cache miss - using fallback data',
		'CACHE_MISS',
		200,
		ErrorSeverity.LOW
	);

	// Example async operation
	const simulateAsyncOperation = () => {
		return new Promise((resolve, reject) => {
			setTimeout(() => {
				if (Math.random() > 0.5) {
					resolve('Operation completed successfully!');
				} else {
					reject(new Error('Operation failed'));
				}
			}, 2000);
		});
	};

	return (
		<div className="p-6 space-y-4 max-w-2xl mx-auto">
			<h2 className="text-2xl font-bold mb-6">Toast Examples</h2>

			{/* Error Toasts by Severity */}
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">Error Toasts by Severity</h3>
				<div className="flex flex-wrap gap-2">
					<Button variant="destructive" onClick={() => showError(criticalError)}>
						Critical Error
					</Button>
					<Button
						variant="destructive"
						onClick={() =>
							showError(highError, {
								onRetry: () => console.log('Retrying high error...'),
							})
						}
					>
						High Error (with Retry)
					</Button>
					<Button variant="outline" onClick={() => showError(mediumError)}>
						Medium Error
					</Button>
					<Button variant="ghost" onClick={() => showError(lowError)}>
						Low Error
					</Button>
				</div>
			</div>

			{/* Success and Info Toasts */}
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">Success & Info Toasts</h3>
				<div className="flex flex-wrap gap-2">
					<Button
						variant="default"
						onClick={() => showSuccess('Operation completed successfully!')}
					>
						Success Toast
					</Button>
					<Button
						variant="secondary"
						onClick={() =>
							showInfo('This is an info message', 'Additional details here')
						}
					>
						Info Toast
					</Button>
					<Button
						variant="outline"
						onClick={() => showWarning('This is a warning', 'Please be careful')}
					>
						Warning Toast
					</Button>
				</div>
			</div>

			{/* Loading and Promise Toasts */}
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">Loading & Promise Toasts</h3>
				<div className="flex flex-wrap gap-2">
					<Button
						variant="outline"
						onClick={() => {
							showLoading('Processing...', 'Please wait');
							setTimeout(() => {
								// Dismiss loading and show success
								showSuccess('Processing complete!');
							}, 3000);
						}}
					>
						Loading Toast
					</Button>
					<Button
						variant="default"
						onClick={() => {
							showPromise(
								simulateAsyncOperation(),
								{
									loading: 'Processing async operation...',
									success: (data: any) => `Success: ${data}`,
									error: (error: any) => `Failed: ${error.message}`,
								},
								{
									loadingDescription: 'This may take a few seconds',
									successDescription: 'The operation completed without errors',
									errorDescription: 'Please try again or contact support',
								}
							);
						}}
					>
						Promise Toast
					</Button>
				</div>
			</div>

			{/* Simple String Errors */}
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">Simple String Errors</h3>
				<div className="flex flex-wrap gap-2">
					<Button variant="destructive" onClick={() => showError('Simple error message')}>
						Simple Error
					</Button>
					<Button
						variant="outline"
						onClick={() =>
							showError(new Error('JavaScript Error'), {
								onRetry: () => console.log('Retrying JS error...'),
								showErrorId: true,
							})
						}
					>
						JS Error with Retry
					</Button>
				</div>
			</div>

			{/* Control Actions */}
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">Control Actions</h3>
				<div className="flex flex-wrap gap-2">
					<Button variant="outline" onClick={dismissAll}>
						Dismiss All Toasts
					</Button>
				</div>
			</div>

			{/* Usage Instructions */}
			<div className="mt-8 p-4 bg-muted rounded-lg">
				<h3 className="text-lg font-semibold mb-2">Usage Instructions</h3>
				<div className="text-sm space-y-2">
					<p>
						<strong>Import:</strong>{' '}
						<code>import &#123; useToast &#125; from '@/contexts/toast-context';</code>
					</p>
					<p>
						<strong>Hook Usage:</strong>{' '}
						<code>const &#123; showError, showSuccess &#125; = useToast();</code>
					</p>
					<p>
						<strong>Error Toast:</strong>{' '}
						<code>showError(error, &#123; onRetry: () =&gt; &#123;&#125; &#125;)</code>
					</p>
					<p>
						<strong>Success Toast:</strong>{' '}
						<code>showSuccess('Message', &#123; duration: 3000 &#125;)</code>
					</p>
					<p>
						<strong>Promise Toast:</strong>{' '}
						<code>showPromise(promise, messages, options)</code>
					</p>
				</div>
			</div>
		</div>
	);
}

export default ToastExample;
